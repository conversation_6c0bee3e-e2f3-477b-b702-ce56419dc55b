import type { Metadata } from 'next'
import PageWrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid
} from '@/components/shapes/AnimatedShapes'
import { RoundedRectangle } from '@/components/shapes/Rectangle'
import { getAllPosts, getFeaturedPosts } from '@/lib/mdx'
import SchemaMarkup from '@/components/seo/SchemaMarkup'

export const metadata: Metadata = {
  title: 'WordPress & Web Development Blog | Navhaus Insights',
  description: 'Expert insights on WordPress development, web development best practices, performance optimization, and modern web technologies from the Navhaus team.',
  keywords: [
    'wordpress blog',
    'web development blog',
    'wordpress development tips',
    'web development insights',
    'wordpress performance',
    'web development best practices',
    'wordpress tutorials',
    'web development guides',
    'navhaus blog'
  ],
  openGraph: {
    title: 'WordPress & Web Development Blog | Navhaus Insights',
    description: 'Expert insights on WordPress development, web development best practices, performance optimization, and modern web technologies from the Navhaus team.',
    url: 'https://navhaus.com/blog',
  },
}

export default function Blog() {
  const allPosts = getAllPosts()
  const featuredPosts = getFeaturedPosts()
  const regularPosts = allPosts.filter(post => !post.featured)

  return (
    <PageWrapper>
      <SchemaMarkup type="blog" />
      {/* Hero Section */}
      <section className="animated-section relative px-6 py-6 md:px-12 lg:px-24 lg:py-0 overflow-hidden flex items-center lg:h-[calc(100vh-6.5rem)]">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-none">
                WordPress & Web Development Insights
              </h1>
              <p className="text-lg md:text-xl leading-relaxed text-gray-700 max-w-lg">
                Expert insights, tutorials, and best practices for WordPress development, 
                web performance, and modern web technologies.
              </p>
            </div>

            <div className="relative h-96 lg:h-[400px]">
              <div className="absolute inset-0">
                <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
              </div>
              <div className="relative flex justify-center items-center h-full">
                <AnimatedSoftCircle size="xl" color="yellow" className="w-32 h-32" animationPreset="gentle" animationIndex={1} />
              </div>
              <div className="absolute top-8 right-8">
                <AnimatedRoundedRectangle width="lg" height="md" color="blue" className="w-16 h-8" animationPreset="flowing" animationIndex={2} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Featured Articles</h2>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
              In-depth guides and insights from our WordPress and web development experts.
            </p>
          </div>

          <div className={`grid gap-8 ${featuredPosts.length === 1 ? 'grid-cols-1 max-w-2xl mx-auto' : 'grid-cols-1 md:grid-cols-2'}`}>
            {featuredPosts.map((post) => (
              <article key={post.slug} className="bg-black rounded-3xl p-8">
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-bauhaus-black">
                    <span className="bg-bauhaus-blue text-bauhaus-white px-3 py-1 rounded-full text-xs font-medium">
                      {post.category}
                    </span>
                    <span className="text-bauhaus-white">{post.readTime}</span>
                  </div>

                  <h3 className="text-xl font-bold text-bauhaus-white leading-tight">
                    <Link href={`/blog/${post.slug}`} className="hover:text-bauhaus-blue transition-colors">
                      {post.title}
                    </Link>
                  </h3>

                  <p className="text-bauhaus-white leading-relaxed">
                    {post.excerpt}
                  </p>

                  <div className="flex items-center justify-between pt-4">
                    <time className="text-sm text-bauhaus-white">
                      {new Date(post.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </time>
                    <Link
                      href={`/blog/${post.slug}`}
                      className="text-bauhaus-white font-medium hover:text-bauhaus-red transition-colors"
                    >
                      Read more →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* All Posts */}
      <section className="hidden animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Latest Articles</h2>
          </div>

          <div className="space-y-8">
            {regularPosts.map((post) => (
              <article key={post.slug} className="bg-bauhaus-black rounded-3xl p-8 hover:bg-bauhaus-blue transition-colors">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
                  <div className="md:col-span-3 space-y-4">
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="bg-bauhaus-yellow text-bauhaus-black px-3 py-1 rounded-full text-xs font-medium">
                        {post.category}
                      </span>
                      <span className="text-bauhaus-white">{post.readTime}</span>
                      <time className="text-bauhaus-white">
                        {new Date(post.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </time>
                    </div>

                    <h3 className="text-xl font-bold leading-tight text-bauhaus-white">
                      <Link href={`/blog/${post.slug}`} className="hover:text-bauhaus-yellow transition-colors">
                        {post.title}
                      </Link>
                    </h3>

                    <p className="text-bauhaus-white leading-relaxed">
                      {post.excerpt}
                    </p>
                  </div>

                  <div className="md:col-span-1 text-right">
                    <Link
                      href={`/blog/${post.slug}`}
                      className="inline-block bg-bauhaus-yellow text-bauhaus-black px-6 py-3 rounded-full font-medium hover:bg-brand-background transition-colors"
                    >
                      Read Article
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup - Hidden for now */}
      <section className="hidden animated-section relative px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-brand-background overflow-hidden">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-display font-bold mb-8">Stay Updated</h2>
          <p className="text-xl text-gray-700 leading-relaxed mb-12 max-w-2xl mx-auto">
            Get the latest WordPress and web development insights delivered to your inbox.
          </p>

          <div className="max-w-md mx-auto">
            <div className="flex gap-4">
              <input
                type="email"
                placeholder="<EMAIL>"
                className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-bauhaus-blue focus:outline-none"
              />
              <button className="btn-red whitespace-nowrap">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto relative z-10">

          {/* Asymmetric Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 items-center">

            {/* Content Area - Offset for visual interest */}
            <div className="lg:col-span-7 lg:col-start-2 space-y-8">
              <div className="space-y-6">
                <h2 className="text-display font-bold leading-tight">
                  Got something worth building?
                </h2>
                <p className="text-xl leading-relaxed text-gray-300 max-w-2xl">
                  Let's make it real. We'll help you strip it down to what matters and bring it to life.
                </p>
              </div>

              {/* CTA with supporting elements */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                <Link href="/contact" className="btn-primary bg-brand-background text-bauhaus-black border-brand-background hover:bg-transparent hover:text-brand-background inline-block">
                  Get in touch
                </Link>

                {/* Supporting visual element */}
                <div className="flex items-center gap-4 text-gray-400">
                  <div className="flex space-x-1">
                    <RoundedRectangle width="sm" height="sm" color="red" className="w-3 h-3" />
                    <RoundedRectangle width="sm" height="sm" color="yellow" className="w-3 h-3" />
                    <RoundedRectangle width="sm" height="sm" color="blue" className="w-3 h-3" />
                  </div>
                  <span className="text-sm">Usually responds within 24 hours</span>
                </div>
              </div>
            </div>

            {/* Visual Element Area */}
            <div className="lg:col-span-4 relative h-64 lg:h-80">
              {/* Background grid */}
              <div className="absolute inset-0 opacity-30">
                <AnimatedSoftGrid className="w-full h-full text-brand-background" opacity="hero" animationPreset="drift" animationIndex={103} />
              </div>

              {/* Main focal shape */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="energetic" animationIndex={104} />
              </div>

              {/* Orbiting elements */}
              <div className="absolute top-8 right-8">
                <AnimatedRoundedRectangle width="lg" height="md" color="yellow" className="w-16 h-8" animationPreset="flowing" animationIndex={105} />
              </div>
              <div className="absolute bottom-12 left-8">
                <AnimatedTriangle size="md" color="blue" direction="up" className="w-10 h-10" animationPreset="dynamic" animationIndex={106} />
              </div>
              <div className="absolute top-16 left-12">
                <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-8 h-4" animationPreset="gentle" animationIndex={107} />
              </div>
              <div className="absolute bottom-8 right-16">
                <AnimatedSoftCircle size="sm" color="blue" className="w-6 h-6" animationPreset="float" animationIndex={108} />
              </div>

              {/* Connecting lines/paths */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none opacity-20">
                <path
                  d="M 60 80 Q 120 40 180 80 Q 240 120 300 80"
                  stroke="currentColor"
                  strokeWidth="1"
                  fill="none"
                  className="text-brand-background"
                />
                <path
                  d="M 40 160 Q 100 200 160 160 Q 220 120 280 160"
                  stroke="currentColor"
                  strokeWidth="1"
                  fill="none"
                  className="text-brand-background"
                />
              </svg>
            </div>
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
