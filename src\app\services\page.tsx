import type { Metadata } from 'next'
import PageWrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid
} from '@/components/shapes/AnimatedShapes'
import SchemaMarkup from '@/components/seo/SchemaMarkup'

export const metadata: Metadata = {
  title: 'WordPress & Web Development Services',
  description: 'Professional WordPress and web development services by Navhaus. We create custom WordPress websites, web applications, and digital solutions. Expert WordPress agency with proven results.',
  keywords: [
    'wordpress development services',
    'custom wordpress website',
    'wordpress agency',
    'web development services',
    'custom website development',
    'web development agency',
    'custom web applications',
    'wordpress specialists',
    'wordpress consulting',
    'react development'
  ],
  alternates: {
    canonical: '/services',
  },
  openGraph: {
    title: 'Navhaus | WordPress & Web Development Services',
    description: 'Professional WordPress and web development services by Navhaus. We create custom WordPress websites, web applications, and digital solutions. Expert WordPress agency with proven results.',
    url: 'https://navhaus.com/services',
  },
}

export default function Services() {
  return (
    <>
      <SchemaMarkup
        type="service"
        data={{
          name: "WordPress & Web Development Services",
          description: "Professional WordPress and web development services including custom WordPress websites, web applications, and digital solutions."
        }}
      />
      <PageWrapper>
      {/* Hero Section */}
      <section className="animated-section relative px-6 py-6 md:px-12 lg:px-24 lg:py-0 overflow-hidden flex items-center lg:h-[calc(100vh-6.5rem)]">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-none">
                WordPress & Web Development Services
              </h1>
              <p className="text-lg md:text-xl leading-relaxed text-gray-700 max-w-lg">
                Custom WordPress websites and modern web applications built from scratch.
                Clean code, modern design, and performance-focused development that drives results.
              </p>
              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>

            <div className="relative h-96 lg:h-[400px]">
              <div className="absolute inset-0">
                <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
              </div>
              <div className="relative flex justify-center items-center h-full">
                <AnimatedSoftCircle size="xl" color="blue" className="w-32 h-32" animationPreset="gentle" animationIndex={1} />
              </div>
              <div className="absolute top-8 right-8">
                <AnimatedRoundedRectangle width="lg" height="md" color="yellow" className="w-16 h-8" animationPreset="flowing" animationIndex={2} />
              </div>
              <div className="absolute bottom-12 left-8">
                <AnimatedTriangle size="md" color="red" direction="up" className="w-10 h-10" animationPreset="dynamic" animationIndex={3} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* WordPress Services Grid */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Our Development Services</h2>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
              From custom WordPress sites to React applications, we handle every aspect of modern web development.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* WordPress Development */}
            <div className="bg-bauhaus-blue text-white p-8 rounded-3xl relative overflow-hidden">
              <div className="absolute inset-0 opacity-20">
                <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="drift" animationIndex={4} />
              </div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mb-6">
                  <div className="w-6 h-6 bg-bauhaus-blue rounded"></div>
                </div>
                <h3 className="text-2xl font-bold mb-4">WordPress Development</h3>
                <p className="text-blue-100 leading-relaxed mb-6">
                  Custom WordPress websites, themes, and functionality. From simple sites to complex
                  enterprise solutions with modern development practices.
                </p>
                <ul className="space-y-2 text-blue-100 text-sm">
                  <li>• Custom WordPress themes & plugins</li>
                  <li>• Gutenberg block development</li>
                  <li>• WordPress performance optimization</li>
                  <li>• Ongoing maintenance & support</li>
                </ul>
              </div>
            </div>

            {/* React & Next.js Development */}
            <div className="bg-bauhaus-red text-white p-8 rounded-3xl relative overflow-hidden">
              <div className="absolute inset-0 opacity-20">
                <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="gentle" animationIndex={5} />
              </div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mb-6">
                  <div className="w-6 h-6 bg-bauhaus-red rounded-full"></div>
                </div>
                <h3 className="text-2xl font-bold mb-4">React & Next.js Development</h3>
                <p className="text-red-100 leading-relaxed mb-6">
                  Modern web applications built with React, Next.js, and TypeScript.
                  Scalable, fast, and built for complex business requirements.
                </p>
                <ul className="space-y-2 text-red-100 text-sm">
                  <li>• Single Page Applications (SPAs)</li>
                  <li>• Progressive Web Apps (PWAs)</li>
                  <li>• E-commerce platforms</li>
                  <li>• Custom dashboards & admin panels</li>
                </ul>
              </div>
            </div>

            {/* Performance & Optimization */}
            <div className="bg-bauhaus-yellow text-bauhaus-black p-8 rounded-3xl relative overflow-hidden">
              <div className="absolute inset-0 opacity-30">
                <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="pulse" animationIndex={6} />
              </div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-bauhaus-black rounded-full flex items-center justify-center mb-6">
                  <div className="w-6 h-6 bg-bauhaus-yellow rounded"></div>
                </div>
                <h3 className="text-2xl font-bold mb-4">Performance & Optimization</h3>
                <p className="text-gray-800 leading-relaxed mb-6">
                  Speed optimization, SEO improvements, and performance audits.
                  We make websites lightning fast and search-engine friendly.
                </p>
                <ul className="space-y-2 text-gray-800 text-sm">
                  <li>• Website speed optimization</li>
                  <li>• SEO implementation & audits</li>
                  <li>• Core Web Vitals improvement</li>
                  <li>• Database & server optimization</li>
                </ul>
              </div>
            </div>

            {/* Design & Consulting */}
            <div className="bg-bauhaus-black text-brand-background p-8 rounded-3xl relative overflow-hidden">
              <div className="absolute inset-0 opacity-20">
                <AnimatedSoftGrid className="w-full h-full text-brand-background" opacity="default" animationPreset="drift" animationIndex={7} />
              </div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-brand-background rounded-full flex items-center justify-center mb-6">
                  <div className="w-6 h-6 bg-bauhaus-black rounded-full"></div>
                </div>
                <h3 className="text-2xl font-bold mb-4">Design & Consulting</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  UI/UX design, technical consulting, and strategic guidance for
                  web projects and digital transformations.
                </p>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li>• UI/UX design & prototyping</li>
                  <li>• Technical architecture planning</li>
                  <li>• Code reviews & audits</li>
                  <li>• Technology stack consulting</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Navhaus */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Why Choose Navhaus</h2>
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              We're web development specialists who understand both the technical and business sides of digital solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-bauhaus-red rounded-full flex-shrink-0 mt-1"></div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Development Experts</h3>
                    <p className="text-gray-300">Years of experience building custom web solutions for businesses of all sizes and industries.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-bauhaus-blue rounded-full flex-shrink-0 mt-1"></div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Modern Technology Stack</h3>
                    <p className="text-gray-300">We use cutting-edge tools and frameworks for maintainable, scalable, and future-proof code.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-bauhaus-yellow rounded-full flex-shrink-0 mt-1"></div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Performance First</h3>
                    <p className="text-gray-300">Every website we build is optimized for speed, SEO, and exceptional user experience.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-brand-background rounded-full flex-shrink-0 mt-1"></div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Long-term Partnership</h3>
                    <p className="text-gray-300">We build relationships, not just websites. Ongoing support and growth-focused solutions.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative h-96">
              <div className="absolute inset-0">
                <AnimatedSoftGrid className="w-full h-full text-brand-background" opacity="hero" animationPreset="drift" animationIndex={10} />
              </div>
              <div className="relative flex justify-center items-center h-full">
                <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="energetic" animationIndex={11} />
              </div>
              <div className="absolute top-8 right-8">
                <AnimatedRoundedRectangle width="lg" height="md" color="yellow" className="w-16 h-8" animationPreset="flowing" animationIndex={12} />
              </div>
              <div className="absolute bottom-12 left-8">
                <AnimatedTriangle size="md" color="blue" direction="up" className="w-10 h-10" animationPreset="dynamic" animationIndex={13} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-brand-background overflow-hidden">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-display font-bold mb-8">Ready to Start Your Project?</h2>
          <p className="text-xl text-gray-700 leading-relaxed mb-12 max-w-2xl mx-auto">
            Let's discuss your web development needs and create a custom solution that grows with your business.
          </p>
          <Link href="/contact" className="btn-red inline-block">
            Get Your Quote
          </Link>
        </div>
      </section>
    </PageWrapper>
    </>
  )
}
