import type { Metadata } from 'next'
import ContactForm from '@/components/contact/ContactForm'
import PageWrapper from '@/components/layout/PageWrapper'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedSoftGrid,
  AnimatedBlob,
  AnimatedPill
} from '@/components/shapes/AnimatedShapes'

export const metadata: Metadata = {
  title: 'Contact',
  description: 'Contact Navhaus for custom WordPress websites and web development services. Get a quote for your WordPress project or web application development.',
  keywords: [
    'contact navhaus',
    'wordpress agency contact',
    'web development quote',
    'custom wordpress website quote',
    'wordpress development services',
    'web development agency contact',
    'hire wordpress developers',
    'wordpress project quote'
  ],
  alternates: {
    canonical: '/contact',
  },
  openGraph: {
    title: 'Navhaus | Contact',
    description: 'Contact Navhaus for custom WordPress websites and web development services. Get a quote for your WordPress project or web application development.',
    url: 'https://navhaus.com/contact',
  },
}

export default function Contact() {

  return (
    <PageWrapper>
      {/* Contact Form Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Background grid */}
          <div className="absolute inset-0 opacity-20">
            <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
          </div>

          {/* Header Section */}
          <div className="grid grid-cols-12 gap-6 mb-16">
            <div className="col-span-12 lg:col-span-8">
              <h1 className="text-5xl md:text-6xl font-bold leading-none mb-6">
                Have something worth building?
              </h1>
              <p className="text-xl leading-relaxed text-gray-700 max-w-2xl">
                Tell us about your project and we'll get back to you within 24 hours.
              </p>
            </div>
            <div className="col-span-12 lg:col-span-4 flex items-center justify-end">
              <div className="relative">
                <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={1} />
                <div className="absolute -bottom-4 -right-4">
                  <AnimatedRoundedRectangle width="lg" height="sm" color="yellow" className="w-16 h-4" animationPreset="drift" animationIndex={2} />
                </div>
              </div>
            </div>
          </div>

          {/* Form Grid Layout */}
          <div className="grid grid-cols-12 gap-6">
            {/* Form Container - Large Block */}
            <div className="col-span-12 lg:col-span-8">
              <div className="bg-brand-background border-3 border-bauhaus-black py-8 md:py-12 rounded-3xl relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={3} />
                </div>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4">
                  <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="gentle" animationIndex={4} />
                </div>

                <div className="relative z-10">
                  <ContactForm />
                </div>
              </div>
            </div>

            {/* Side Information Card */}
            <div className="col-span-12 lg:col-span-4">
              {/* Response Time Card */}
              <div className="bg-bauhaus-yellow text-bauhaus-black p-8 rounded-3xl relative overflow-hidden h-full flex flex-col justify-center">
                <div className="absolute bottom-4 right-4">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="black" className="w-4 h-4" animationPreset="gentle" animationIndex={5} />
                </div>
                <div className="relative z-10 text-center">
                  <div className="text-6xl font-bold mb-4">24h</div>
                  <p className="text-xl leading-tight">
                    Response time for all inquiries
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-16 left-16 opacity-10">
          <AnimatedBlob color="blue" className="w-32 h-32" animationPreset="drift" animationIndex={9} />
        </div>
        <div className="absolute bottom-20 right-20 opacity-15">
          <AnimatedSoftCircle size="lg" color="yellow" className="w-20 h-20" animationPreset="gentle" animationIndex={10} />
        </div>
      </section>

      {/* Alternative Contact Methods */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-bauhaus-white overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Content Section */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h2 className="text-4xl md:text-5xl font-bold leading-none">
                  Or reach out directly
                </h2>
                <p className="text-xl leading-relaxed opacity-90 max-w-lg">
                  We like projects that are sharp, fast, and meaningful.
                  If that's what you've got, we should talk.
                </p>
              </div>

              {/* Contact Cards */}
              <div className="space-y-4">
                <div className="bg-bauhaus-blue text-bauhaus-white p-6 rounded-3xl relative overflow-hidden">
                  <div className="absolute bottom-2 right-2">
                    <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={3} />
                  </div>
                  <div className="relative z-10">
                    <h3 className="font-bold mb-2">Email us directly</h3>
                    <p className="text-lg"><EMAIL></p>
                  </div>
                </div>

                <div className="bg-bauhaus-yellow text-bauhaus-black p-6 rounded-3xl relative overflow-hidden">
                  <div className="absolute top-2 right-2">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="gentle" animationIndex={4} />
                  </div>
                  <div className="relative z-10">
                    <h3 className="font-bold mb-2">Schedule a call</h3>
                    <p className="text-lg">Book 30 minutes to discuss your project</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Visual Composition */}
            <div className="relative h-96 lg:h-[500px]">
              {/* Background grid */}
              <div className="absolute inset-0 opacity-20">
                <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="subtle" animationIndex={5} />
              </div>

              {/* Main focal elements */}
              <div className="absolute top-16 right-12">
                <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={6} />
              </div>

              <div className="absolute bottom-20 left-8">
                <AnimatedBlob color="blue" className="w-32 h-32" animationPreset="flowing" animationIndex={7} />
              </div>

              <div className="absolute top-32 left-16">
                <AnimatedPill color="yellow" className="w-20 h-8" animationPreset="drift" animationIndex={8} />
              </div>

              <div className="absolute bottom-32 right-20">
                <AnimatedRoundedRectangle width="lg" height="md" color="white" className="w-16 h-12" animationPreset="gentle" animationIndex={9} />
              </div>

              {/* Smaller decorative elements */}
              <div className="absolute top-8 left-32 opacity-80">
                <AnimatedSoftCircle size="sm" color="yellow" className="w-6 h-6" animationPreset="energetic" animationIndex={10} />
              </div>

              <div className="absolute bottom-8 right-8 opacity-70">
                <AnimatedPill color="red" className="w-12 h-4" animationPreset="float" animationIndex={11} />
              </div>
            </div>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-16 left-16 opacity-10">
          <AnimatedBlob color="yellow" className="w-24 h-24" animationPreset="drift" animationIndex={12} />
        </div>
        <div className="absolute bottom-20 right-20 opacity-8">
          <AnimatedSoftCircle size="lg" color="red" className="w-20 h-20" animationPreset="gentle" animationIndex={13} />
        </div>
      </section>
    </PageWrapper>
  )
}
